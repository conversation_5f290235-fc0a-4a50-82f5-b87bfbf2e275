<template>
  <div class="order-monitoring-page">
    <div class="chart-container">
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="chart-box">
            <div class="chart-title">各渠道订单数量统计</div>
            <div class="chart-content" style="height: 300px;">
              <ChartRender v-if="barOption" :option="barOption" />
            </div>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="chart-box">
            <div class="chart-title">订单数量时间趋势</div>
            <div class="chart-content" style="height: 300px;">
              <ChartRender v-if="lineOption" :option="lineOption" />
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <div class="table-container">
      <m-table-list
        ref="tableList"
        @clearSearchItem="clearSearchItem"
        @afterFilterLayoutTable="afterFilterLayoutTable"
        :columns="tableColumns"
        :search-show-default="false"
        :cache-columns-key="$route.name + $options.name"
        :params="params"
        :total="result.total"
        @loadData="loadData"
        @refresh-table="calculationTableHeight"
        @refresh="handleFilter"
        @collapseChange="searchVisible = !searchVisible"
        @showColumnsChange="showColumnsChange"
      >
        <template v-slot:search>
          <el-form-item :label="$t('order.monitoring.channelName')">
            <el-input
              v-model="params.condition.channelName"
              :placeholder="$t('hint.pleaseInput')"
              clearable
              @keyup.enter.native="handleFilter"
            ></el-input>
          </el-form-item>
          <el-form-item :label="$t('order.monitoring.channelAccount')">
            <el-input
              v-model="params.condition.channelAccount"
              :placeholder="$t('hint.pleaseInput')"
              clearable
              @keyup.enter.native="handleFilter"
            ></el-input>
          </el-form-item>
          <el-form-item :label="$t('order.monitoring.timeRange')">
            <el-date-picker
              v-model="dateRange"
              type="datetimerange"
              :range-separator="$t('common.to')"
              :start-placeholder="$t('common.startDate')"
              :end-placeholder="$t('common.endDate')"
              value-format="yyyy-MM-dd HH:mm:ss"
              @change="handleDateRangeChange"
            ></el-date-picker>
          </el-form-item>
        </template>
        <template v-slot:table>
          <el-table
            :data="result.list"
            v-loading="result.loading"
            :height="tableHeight"
            ref="table"
            @sort-change="sortChangeHandle"
            highlight-current-row
            resizable
            border
            :header-cell-style="tableHeaderStyle"
            :cell-style="tableCell"
            :row-key="getRowKeys"
            class="c-table"
            @header-dragend="
              (newWidth, oldWidth, column) =>
                tableHeaderDragend(newWidth, column.property, 'order-monitoring')
            "
            :class="{ 'c-table__search--hidden': !searchVisible }"
          >
            <template v-for="(column, index) of showColumns">
              <m-table-column-show :key="index" :column="column" @refresh="handleFilter">
              </m-table-column-show>
            </template>
          </el-table>
        </template>
      </m-table-list>
    </div>
  </div>
</template>

<script>
import ChartRender from '@/components/chart-render/index.vue';
import CommonUtil from '@/utils/common';
import elementUtil from '@/utils/element';
import { mapGetters } from 'vuex';

class Condition {
  channelName = '';
  channelAccount = '';
  startDate = '';
  endDate = '';
}

export default {
  name: 'OrderMonitoring',
  components: {
    ChartRender
  },
  data() {
    return {
      searchVisible: true,
      tableHeight: 500,
      dateRange: [],
      barOption: {},
      lineOption: {},
      showColumns: [],
      tableColumns: [
        {
          label: '渠道名称',
          field: 'ChannelName',
          hide: false,
          width: null,
          align: 'center',
          formatter: (row) => row.ChannelName.value
        },
        {
          label: '渠道账号',
          field: 'ChannelAccount',
          hide: false,
          width: null,
          align: 'center',
          formatter: (row) => row.ChannelAccount.value
        },
        {
          label: '总订单数量',
          field: 'channelOrderCount',
          hide: false,
          width: null,
          align: 'center',
          formatter: (row) => row.channelOrderCount.value
        },
        {
          label: '活跃订单数量',
          field: 'currentChannelOrderCount',
          hide: false,
          width: null,
          align: 'center',
          formatter: (row) => row.currentChannelOrderCount.value
        },
        {
          label: '中间状态订单数量',
          field: 'currentOrderIntermediateCount',
          hide: false,
          width: null,
          align: 'center',
          formatter: (row) => row.currentOrderIntermediateCount.value
        },
        {
          label: '已发货订单数量',
          field: 'currentInvoiceCountShippedCount',
          hide: false,
          width: null,
          align: 'center',
          formatter: (row) => row.currentInvoiceCountShippedCount.value
        },
        {
          label: '未发货订单数量',
          field: 'currentInvoiceCountnNotShippedCount',
          hide: false,
          width: null,
          align: 'center',
          formatter: (row) => row.currentInvoiceCountnNotShippedCount.value
        },
        {
          label: '记录时间',
          field: 'EnterDate',
          hide: false,
          width: null,
          align: 'center',
          formatter: (row) => row.EnterDate.value
        }
      ],
      result: {
        loading: false,
        list: [],
        total: 0
      },
      params: {
        current: 1,
        size: 10,
        condition: new Condition()
      },
      // 模拟数据
      mockData: [
        {
          Id: { value: 1, remark: "日志id" },
          ChannelName: { value: "emks-amazon", remark: "渠道名称，如电商平台标识" },
          ChannelAccount: { value: "<EMAIL>", remark: "渠道账号，用于登录或关联账户" },
          channelOrderCount: { value: 10, remark: "渠道总订单数量" },
          currentChannelOrderCount: { value: 5, remark: "当前渠道活跃订单数量" },
          orderIntermediateCount: { value: 42, remark: "处于中间状态的订单数量（如处理中）" },
          currentOrderIntermediateCount: { value: 12, remark: "当前处于中间状态的订单数量" },
          invoiceDeliveryTimeoutCount: { value: 47, remark: "配送超时的订单数量" },
          invoiceCount: { value: 42, remark: "已发货订单数量" },
          invoiceCountShippedCount: { value: 31, remark: "已发货的订单数量" },
          invoiceCountnNotShippedCount: { value: 11, remark: "未发货的发票数量" },
          currentInvoiceCount: { value: 8, remark: "当前处理中的发票数量" },
          currentInvoiceCountShippedCount: { value: 6, remark: "当前已发货的发票数量" },
          currentInvoiceCountnNotShippedCount: { value: 2, remark: "当前未发货的发票数量" },
          EnterDate: { value: "2025-06-04 03:00:18.730", remark: "记录创建时间" },
          LastUpdate: { value: "2025-06-04 03:00:18.730", remark: "最后更新时间" },
          BOPS_CompanyID: { value: 10001, remark: "关联公司ID，用于企业级数据区分" }
        },
        {
          Id: { value: 2, remark: "日志id" },
          ChannelName: { value: "emks-ebay", remark: "渠道名称，如电商平台标识" },
          ChannelAccount: { value: "<EMAIL>", remark: "渠道账号，用于登录或关联账户" },
          channelOrderCount: { value: 15, remark: "渠道总订单数量" },
          currentChannelOrderCount: { value: 8, remark: "当前渠道活跃订单数量" },
          orderIntermediateCount: { value: 35, remark: "处于中间状态的订单数量（如处理中）" },
          currentOrderIntermediateCount: { value: 10, remark: "当前处于中间状态的订单数量" },
          invoiceDeliveryTimeoutCount: { value: 38, remark: "配送超时的订单数量" },
          invoiceCount: { value: 35, remark: "已发货订单数量" },
          invoiceCountShippedCount: { value: 28, remark: "已发货的订单数量" },
          invoiceCountnNotShippedCount: { value: 7, remark: "未发货的发票数量" },
          currentInvoiceCount: { value: 6, remark: "当前处理中的发票数量" },
          currentInvoiceCountShippedCount: { value: 4, remark: "当前已发货的发票数量" },
          currentInvoiceCountnNotShippedCount: { value: 2, remark: "当前未发货的发票数量" },
          EnterDate: { value: "2025-06-04 09:00:18.730", remark: "记录创建时间" },
          LastUpdate: { value: "2025-06-04 09:00:18.730", remark: "最后更新时间" },
          BOPS_CompanyID: { value: 10001, remark: "关联公司ID，用于企业级数据区分" }
        },
        {
          Id: { value: 3, remark: "日志id" },
          ChannelName: { value: "emks-walmart", remark: "渠道名称，如电商平台标识" },
          ChannelAccount: { value: "<EMAIL>", remark: "渠道账号，用于登录或关联账户" },
          channelOrderCount: { value: 8, remark: "渠道总订单数量" },
          currentChannelOrderCount: { value: 3, remark: "当前渠道活跃订单数量" },
          orderIntermediateCount: { value: 25, remark: "处于中间状态的订单数量（如处理中）" },
          currentOrderIntermediateCount: { value: 8, remark: "当前处于中间状态的订单数量" },
          invoiceDeliveryTimeoutCount: { value: 30, remark: "配送超时的订单数量" },
          invoiceCount: { value: 25, remark: "已发货订单数量" },
          invoiceCountShippedCount: { value: 20, remark: "已发货的订单数量" },
          invoiceCountnNotShippedCount: { value: 5, remark: "未发货的发票数量" },
          currentInvoiceCount: { value: 4, remark: "当前处理中的发票数量" },
          currentInvoiceCountShippedCount: { value: 3, remark: "当前已发货的发票数量" },
          currentInvoiceCountnNotShippedCount: { value: 1, remark: "当前未发货的发票数量" },
          EnterDate: { value: "2025-06-04 15:00:18.730", remark: "记录创建时间" },
          LastUpdate: { value: "2025-06-04 15:00:18.730", remark: "最后更新时间" },
          BOPS_CompanyID: { value: 10001, remark: "关联公司ID，用于企业级数据区分" }
        },
        {
          Id: { value: 4, remark: "日志id" },
          ChannelName: { value: "emks-amazon", remark: "渠道名称，如电商平台标识" },
          ChannelAccount: { value: "<EMAIL>", remark: "渠道账号，用于登录或关联账户" },
          channelOrderCount: { value: 12, remark: "渠道总订单数量" },
          currentChannelOrderCount: { value: 6, remark: "当前渠道活跃订单数量" },
          orderIntermediateCount: { value: 45, remark: "处于中间状态的订单数量（如处理中）" },
          currentOrderIntermediateCount: { value: 15, remark: "当前处于中间状态的订单数量" },
          invoiceDeliveryTimeoutCount: { value: 50, remark: "配送超时的订单数量" },
          invoiceCount: { value: 45, remark: "已发货订单数量" },
          invoiceCountShippedCount: { value: 35, remark: "已发货的订单数量" },
          invoiceCountnNotShippedCount: { value: 10, remark: "未发货的发票数量" },
          currentInvoiceCount: { value: 9, remark: "当前处理中的发票数量" },
          currentInvoiceCountShippedCount: { value: 7, remark: "当前已发货的发票数量" },
          currentInvoiceCountnNotShippedCount: { value: 2, remark: "当前未发货的发票数量" },
          EnterDate: { value: "2025-06-04 21:00:18.730", remark: "记录创建时间" },
          LastUpdate: { value: "2025-06-04 21:00:18.730", remark: "最后更新时间" },
          BOPS_CompanyID: { value: 10001, remark: "关联公司ID，用于企业级数据区分" }
        },
        {
          Id: { value: 5, remark: "日志id" },
          ChannelName: { value: "emks-amazon", remark: "渠道名称，如电商平台标识" },
          ChannelAccount: { value: "<EMAIL>", remark: "渠道账号，用于登录或关联账户" },
          channelOrderCount: { value: 14, remark: "渠道总订单数量" },
          currentChannelOrderCount: { value: 7, remark: "当前渠道活跃订单数量" },
          orderIntermediateCount: { value: 48, remark: "处于中间状态的订单数量（如处理中）" },
          currentOrderIntermediateCount: { value: 18, remark: "当前处于中间状态的订单数量" },
          invoiceDeliveryTimeoutCount: { value: 52, remark: "配送超时的订单数量" },
          invoiceCount: { value: 48, remark: "已发货订单数量" },
          invoiceCountShippedCount: { value: 38, remark: "已发货的订单数量" },
          invoiceCountnNotShippedCount: { value: 10, remark: "未发货的发票数量" },
          currentInvoiceCount: { value: 10, remark: "当前处理中的发票数量" },
          currentInvoiceCountShippedCount: { value: 8, remark: "当前已发货的发票数量" },
          currentInvoiceCountnNotShippedCount: { value: 2, remark: "当前未发货的发票数量" },
          EnterDate: { value: "2025-06-05 03:00:18.730", remark: "记录创建时间" },
          LastUpdate: { value: "2025-06-05 03:00:18.730", remark: "最后更新时间" },
          BOPS_CompanyID: { value: 10001, remark: "关联公司ID，用于企业级数据区分" }
        }
      ]
    };
  },
  mounted() {
    this.calculationTableHeight();
    this.initCharts();
    this.loadData();
  },
  methods: {
    initCharts() {
      // 初始化图表
      this.barOption = this.getBarOption();
      this.lineOption = this.getLineOption();
    },
    getBarOption() {
      // 获取所有不同的渠道
      const channels = [...new Set(this.mockData.map(item => item.ChannelName.value))];
      
      // 准备图表数据
      const totalOrders = [];
      const activeOrders = [];
      
      channels.forEach(channel => {
        const channelData = this.mockData.filter(item => item.ChannelName.value === channel);
        const latestData = channelData[0]; // 假设数据已按时间排序
        
        totalOrders.push(latestData.channelOrderCount.value);
        activeOrders.push(latestData.currentChannelOrderCount.value);
      });
      
      return {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          data: ['总订单数量', '活跃订单数量']
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: channels
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '总订单数量',
            type: 'bar',
            data: totalOrders
          },
          {
            name: '活跃订单数量',
            type: 'bar',
            data: activeOrders
          }
        ]
      };
    },
    getLineOption() {
      // 获取所有不同的渠道
      const channels = [...new Set(this.mockData.map(item => item.ChannelName.value))];
      
      // 准备图表数据
      const series = channels.map(channel => {
        const channelData = this.mockData.filter(item => item.ChannelName.value === channel);
        
        // 获取该渠道的订单数量数据
        const data = channelData.map(item => item.channelOrderCount.value);
        
        return {
          name: channel,
          type: 'line',
          data: data
        };
      });
      
      // 获取时间点（假设所有渠道的时间点相同）
      const times = this.mockData
        .filter(item => item.ChannelName.value === channels[0])
        .map(item => {
          const date = new Date(item.EnterDate.value);
          return `${date.getMonth() + 1}-${date.getDate()} ${date.getHours()}:${date.getMinutes()}`;
        });
      
      return {
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: channels
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: times
        },
        yAxis: {
          type: 'value'
        },
        series: series
      };
    },
    getRowKeys(row) {
      return row.Id.value;
    },
    calculationTableHeight() {
      this.$nextTick(() => {
        if (this.$refs.table) {
          const offsetTop = this.$refs.table.$el.offsetTop;
          const windowHeight = window.innerHeight;
          this.tableHeight = windowHeight - offsetTop - 100;
        }
      });
    },
    handleDateRangeChange(val) {
      if (val) {
        this.params.condition.startDate = val[0];
        this.params.condition.endDate = val[1];
      } else {
        this.params.condition.startDate = '';
        this.params.condition.endDate = '';
      }
    },
    clearSearchItem() {
      this.params.condition = new Condition();
      this.dateRange = [];
      this.handleFilter();
    },
    afterFilterLayoutTable() {
      this.calculationTableHeight();
    },
    showColumnsChange(val) {
      this.showColumns = val;
    },
    handleFilter() {
      this.params.current = 1;
      this.loadData();
    },
    sortChangeHandle({ column, prop, order }) {
      if (order) {
        this.params.sorts = [
          {
            field: CommonUtil.camelToSnake ? CommonUtil.camelToSnake(prop) : prop,
            asc: order === 'ascending',
          },
        ];
      } else {
        delete this.params.sorts;
      }
      this.loadData();
    },
    tableHeaderStyle() {
      return {
        background: '#F5F7FA',
        color: '#606266',
        height: '50px',
        padding: '0',
        fontWeight: 'bold'
      };
    },
    tableCell() {
      return {
        padding: '5px 0'
      };
    },
    tableHeaderDragend(newWidth, prop, key) {
      // 实现表头拖拽调整列宽的逻辑
      console.log('Column width changed:', newWidth, prop, key);
    },
    loadData() {
      this.result.loading = true;
      
      // 模拟API调用
      setTimeout(() => {
        // 过滤数据
        let filteredData = [...this.mockData];
        const condition = this.params.condition;
        
        if (condition.channelName) {
          filteredData = filteredData.filter(item => 
            item.ChannelName.value.toLowerCase().includes(condition.channelName.toLowerCase())
          );
        }
        
        if (condition.channelAccount) {
          filteredData = filteredData.filter(item => 
            item.ChannelAccount.value.toLowerCase().includes(condition.channelAccount.toLowerCase())
          );
        }
        
        if (condition.startDate && condition.endDate) {
          filteredData = filteredData.filter(item => {
            const date = new Date(item.EnterDate.value);
            const start = new Date(condition.startDate);
            const end = new Date(condition.endDate);
            return date >= start && date <= end;
          });
        }
        
        // 分页
        const start = (this.params.current - 1) * this.params.size;
        const end = start + this.params.size;
        const paginatedData = filteredData.slice(start, end);
        
        this.result.list = paginatedData;
        this.result.total = filteredData.length

