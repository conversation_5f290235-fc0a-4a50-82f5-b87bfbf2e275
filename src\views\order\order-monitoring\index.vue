<template>
  <div class="order-monitoring-page">
    <div class="chart-container">
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="chart-box">
            <div class="chart-title">各渠道订单数量统计</div>
            <div class="chart-content" style="height: 300px;">
              <ChartRender :option="barOption" />
            </div>
          </div>
        </el-col>
        <el-col :span="12">
          <div class="chart-box">
            <div class="chart-title">订单数量时间趋势</div>
            <div class="chart-content" style="height: 300px;">
              <ChartRender :option="lineOption" />
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <div class="table-container">
      <m-table-list
        ref="tableList"
        @clearSearchItem="clearSearchItem"
        @afterFilterLayoutTable="afterFilterLayoutTable"
        :columns="columns"
        v-model="result.columns"
        :search-show-default="false"
        :cache-columns-key="$route.name + $options.name"
        :params="params"
        :total="result.total"
        @loadData="loadData"
        @refresh="handleFilter"
        @collapseChange="searchVisible = !searchVisible"
      >
        <template v-slot:search>
          <el-form-item label="渠道名称">
            <el-input
              v-model="params.condition.channelName"
              placeholder="请输入渠道名称"
              clearable
              @keyup.enter.native="handleFilter"
            ></el-input>
          </el-form-item>
          <el-form-item label="渠道账号">
            <el-input
              v-model="params.condition.channelAccount"
              placeholder="请输入渠道账号"
              clearable
              @keyup.enter.native="handleFilter"
            ></el-input>
          </el-form-item>
          <el-form-item label="时间范围">
            <el-date-picker
              v-model="dateRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd HH:mm:ss"
              @change="handleDateRangeChange"
            ></el-date-picker>
          </el-form-item>
        </template>
        <template v-slot:table>
          <el-table
            :data="result.list"
            v-loading="result.loading"
            :height="tableHeight"
            ref="table"
            @sort-change="sortChangeHandle"
            highlight-current-row
            resizable
            border
            :header-cell-style="getTableHeaderStyle"
            :cell-style="getTableCell"
            :row-key="getRowKeys"
            class="c-table"
            @header-dragend="
              (newWidth, oldWidth, column) =>
                tableHeaderDragend(newWidth, column.property, 'order-monitoring')
            "
            :class="{ 'c-table__search--hidden': !searchVisible }"
          >
            <template>
              <m-table-column-show
                v-for="(column, index) of showColumns"
                :key="index"
                :column="column"
                @refresh="handleFilter"
              >
              </m-table-column-show>
            </template>
          </el-table>
        </template>
      </m-table-list>
    </div>
  </div>
</template>

<script>
import ChartRender from '@/components/chart-render/index.vue';
import CommonUtil from '@/utils/common';
import elementUtil from '@/utils/element';
import { mapGetters } from 'vuex';

class Condition {
  channelName = '';
  channelAccount = '';
  startDate = '';
  endDate = '';
}

export default {
  name: 'OrderMonitoring',
  components: {
    ChartRender
  },
  data() {
    return {
      searchVisible: true,
      tableHeight: 500,
      dateRange: [],
      barOption: {},
      lineOption: {},
      result: {
        loading: false,
        list: [],
        total: 0,
        columns: []
      },
      params: {
        current: 1,
        size: 10,
        condition: new Condition()
      },
      // 模拟数据 - 10个渠道
      mockData: [
        // Amazon 渠道数据
        {
          Id: { value: 1, remark: "日志id" },
          ChannelName: { value: "emks-amazon", remark: "渠道名称，如电商平台标识" },
          ChannelAccount: { value: "<EMAIL>", remark: "渠道账号，用于登录或关联账户" },
          channelOrderCount: { value: 120, remark: "渠道总订单数量" },
          currentChannelOrderCount: { value: 85, remark: "当前渠道活跃订单数量" },
          orderIntermediateCount: { value: 42, remark: "处于中间状态的订单数量（如处理中）" },
          currentOrderIntermediateCount: { value: 25, remark: "当前处于中间状态的订单数量" },
          invoiceDeliveryTimeoutCount: { value: 8, remark: "配送超时的订单数量" },
          invoiceCount: { value: 95, remark: "已发货订单数量" },
          invoiceCountShippedCount: { value: 87, remark: "已发货的订单数量" },
          invoiceCountnNotShippedCount: { value: 8, remark: "未发货的发票数量" },
          currentInvoiceCount: { value: 15, remark: "当前处理中的发票数量" },
          currentInvoiceCountShippedCount: { value: 78, remark: "当前已发货的发票数量" },
          currentInvoiceCountnNotShippedCount: { value: 7, remark: "当前未发货的发票数量" },
          EnterDate: { value: "2025-06-15 03:00:18.730", remark: "记录创建时间" },
          LastUpdate: { value: "2025-06-15 03:00:18.730", remark: "最后更新时间" },
          BOPS_CompanyID: { value: 10001, remark: "关联公司ID，用于企业级数据区分" }
        },
        // eBay 渠道数据
        {
          Id: { value: 2, remark: "日志id" },
          ChannelName: { value: "emks-ebay", remark: "渠道名称，如电商平台标识" },
          ChannelAccount: { value: "<EMAIL>", remark: "渠道账号，用于登录或关联账户" },
          channelOrderCount: { value: 95, remark: "渠道总订单数量" },
          currentChannelOrderCount: { value: 68, remark: "当前渠道活跃订单数量" },
          orderIntermediateCount: { value: 35, remark: "处于中间状态的订单数量（如处理中）" },
          currentOrderIntermediateCount: { value: 22, remark: "当前处于中间状态的订单数量" },
          invoiceDeliveryTimeoutCount: { value: 5, remark: "配送超时的订单数量" },
          invoiceCount: { value: 72, remark: "已发货订单数量" },
          invoiceCountShippedCount: { value: 67, remark: "已发货的订单数量" },
          invoiceCountnNotShippedCount: { value: 5, remark: "未发货的发票数量" },
          currentInvoiceCount: { value: 12, remark: "当前处理中的发票数量" },
          currentInvoiceCountShippedCount: { value: 60, remark: "当前已发货的发票数量" },
          currentInvoiceCountnNotShippedCount: { value: 8, remark: "当前未发货的发票数量" },
          EnterDate: { value: "2025-06-15 09:00:18.730", remark: "记录创建时间" },
          LastUpdate: { value: "2025-06-15 09:00:18.730", remark: "最后更新时间" },
          BOPS_CompanyID: { value: 10001, remark: "关联公司ID，用于企业级数据区分" }
        },
        // Walmart 渠道数据
        {
          Id: { value: 3, remark: "日志id" },
          ChannelName: { value: "emks-walmart", remark: "渠道名称，如电商平台标识" },
          ChannelAccount: { value: "<EMAIL>", remark: "渠道账号，用于登录或关联账户" },
          channelOrderCount: { value: 78, remark: "渠道总订单数量" },
          currentChannelOrderCount: { value: 52, remark: "当前渠道活跃订单数量" },
          orderIntermediateCount: { value: 25, remark: "处于中间状态的订单数量（如处理中）" },
          currentOrderIntermediateCount: { value: 18, remark: "当前处于中间状态的订单数量" },
          invoiceDeliveryTimeoutCount: { value: 3, remark: "配送超时的订单数量" },
          invoiceCount: { value: 55, remark: "已发货订单数量" },
          invoiceCountShippedCount: { value: 52, remark: "已发货的订单数量" },
          invoiceCountnNotShippedCount: { value: 3, remark: "未发货的发票数量" },
          currentInvoiceCount: { value: 8, remark: "当前处理中的发票数量" },
          currentInvoiceCountShippedCount: { value: 47, remark: "当前已发货的发票数量" },
          currentInvoiceCountnNotShippedCount: { value: 5, remark: "当前未发货的发票数量" },
          EnterDate: { value: "2025-06-15 15:00:18.730", remark: "记录创建时间" },
          LastUpdate: { value: "2025-06-15 15:00:18.730", remark: "最后更新时间" },
          BOPS_CompanyID: { value: 10001, remark: "关联公司ID，用于企业级数据区分" }
        },
        // Shopify 渠道数据
        {
          Id: { value: 4, remark: "日志id" },
          ChannelName: { value: "emks-shopify", remark: "渠道名称，如电商平台标识" },
          ChannelAccount: { value: "<EMAIL>", remark: "渠道账号，用于登录或关联账户" },
          channelOrderCount: { value: 65, remark: "渠道总订单数量" },
          currentChannelOrderCount: { value: 43, remark: "当前渠道活跃订单数量" },
          orderIntermediateCount: { value: 20, remark: "处于中间状态的订单数量（如处理中）" },
          currentOrderIntermediateCount: { value: 15, remark: "当前处于中间状态的订单数量" },
          invoiceDeliveryTimeoutCount: { value: 2, remark: "配送超时的订单数量" },
          invoiceCount: { value: 48, remark: "已发货订单数量" },
          invoiceCountShippedCount: { value: 46, remark: "已发货的订单数量" },
          invoiceCountnNotShippedCount: { value: 2, remark: "未发货的发票数量" },
          currentInvoiceCount: { value: 6, remark: "当前处理中的发票数量" },
          currentInvoiceCountShippedCount: { value: 40, remark: "当前已发货的发票数量" },
          currentInvoiceCountnNotShippedCount: { value: 3, remark: "当前未发货的发票数量" },
          EnterDate: { value: "2025-06-15 21:00:18.730", remark: "记录创建时间" },
          LastUpdate: { value: "2025-06-15 21:00:18.730", remark: "最后更新时间" },
          BOPS_CompanyID: { value: 10001, remark: "关联公司ID，用于企业级数据区分" }
        },
        // Etsy 渠道数据
        {
          Id: { value: 5, remark: "日志id" },
          ChannelName: { value: "emks-etsy", remark: "渠道名称，如电商平台标识" },
          ChannelAccount: { value: "<EMAIL>", remark: "渠道账号，用于登录或关联账户" },
          channelOrderCount: { value: 42, remark: "渠道总订单数量" },
          currentChannelOrderCount: { value: 28, remark: "当前渠道活跃订单数量" },
          orderIntermediateCount: { value: 15, remark: "处于中间状态的订单数量（如处理中）" },
          currentOrderIntermediateCount: { value: 10, remark: "当前处于中间状态的订单数量" },
          invoiceDeliveryTimeoutCount: { value: 1, remark: "配送超时的订单数量" },
          invoiceCount: { value: 32, remark: "已发货订单数量" },
          invoiceCountShippedCount: { value: 31, remark: "已发货的订单数量" },
          invoiceCountnNotShippedCount: { value: 1, remark: "未发货的发票数量" },
          currentInvoiceCount: { value: 4, remark: "当前处理中的发票数量" },
          currentInvoiceCountShippedCount: { value: 27, remark: "当前已发货的发票数量" },
          currentInvoiceCountnNotShippedCount: { value: 1, remark: "当前未发货的发票数量" },
          EnterDate: { value: "2025-06-16 03:00:18.730", remark: "记录创建时间" },
          LastUpdate: { value: "2025-06-16 03:00:18.730", remark: "最后更新时间" },
          BOPS_CompanyID: { value: 10001, remark: "关联公司ID，用于企业级数据区分" }
        },
        // Target 渠道数据
        {
          Id: { value: 6, remark: "日志id" },
          ChannelName: { value: "emks-target", remark: "渠道名称，如电商平台标识" },
          ChannelAccount: { value: "<EMAIL>", remark: "渠道账号，用于登录或关联账户" },
          channelOrderCount: { value: 88, remark: "渠道总订单数量" },
          currentChannelOrderCount: { value: 61, remark: "当前渠道活跃订单数量" },
          orderIntermediateCount: { value: 30, remark: "处于中间状态的订单数量（如处理中）" },
          currentOrderIntermediateCount: { value: 20, remark: "当前处于中间状态的订单数量" },
          invoiceDeliveryTimeoutCount: { value: 4, remark: "配送超时的订单数量" },
          invoiceCount: { value: 65, remark: "已发货订单数量" },
          invoiceCountShippedCount: { value: 61, remark: "已发货的订单数量" },
          invoiceCountnNotShippedCount: { value: 4, remark: "未发货的发票数量" },
          currentInvoiceCount: { value: 9, remark: "当前处理中的发票数量" },
          currentInvoiceCountShippedCount: { value: 56, remark: "当前已发货的发票数量" },
          currentInvoiceCountnNotShippedCount: { value: 5, remark: "当前未发货的发票数量" },
          EnterDate: { value: "2025-06-16 09:00:18.730", remark: "记录创建时间" },
          LastUpdate: { value: "2025-06-16 09:00:18.730", remark: "最后更新时间" },
          BOPS_CompanyID: { value: 10001, remark: "关联公司ID，用于企业级数据区分" }
        },
        // Best Buy 渠道数据
        {
          Id: { value: 7, remark: "日志id" },
          ChannelName: { value: "emks-bestbuy", remark: "渠道名称，如电商平台标识" },
          ChannelAccount: { value: "<EMAIL>", remark: "渠道账号，用于登录或关联账户" },
          channelOrderCount: { value: 56, remark: "渠道总订单数量" },
          currentChannelOrderCount: { value: 38, remark: "当前渠道活跃订单数量" },
          orderIntermediateCount: { value: 18, remark: "处于中间状态的订单数量（如处理中）" },
          currentOrderIntermediateCount: { value: 12, remark: "当前处于中间状态的订单数量" },
          invoiceDeliveryTimeoutCount: { value: 2, remark: "配送超时的订单数量" },
          invoiceCount: { value: 42, remark: "已发货订单数量" },
          invoiceCountShippedCount: { value: 40, remark: "已发货的订单数量" },
          invoiceCountnNotShippedCount: { value: 2, remark: "未发货的发票数量" },
          currentInvoiceCount: { value: 5, remark: "当前处理中的发票数量" },
          currentInvoiceCountShippedCount: { value: 35, remark: "当前已发货的发票数量" },
          currentInvoiceCountnNotShippedCount: { value: 3, remark: "当前未发货的发票数量" },
          EnterDate: { value: "2025-06-16 15:00:18.730", remark: "记录创建时间" },
          LastUpdate: { value: "2025-06-16 15:00:18.730", remark: "最后更新时间" },
          BOPS_CompanyID: { value: 10001, remark: "关联公司ID，用于企业级数据区分" }
        },
        // Newegg 渠道数据
        {
          Id: { value: 8, remark: "日志id" },
          ChannelName: { value: "emks-newegg", remark: "渠道名称，如电商平台标识" },
          ChannelAccount: { value: "<EMAIL>", remark: "渠道账号，用于登录或关联账户" },
          channelOrderCount: { value: 34, remark: "渠道总订单数量" },
          currentChannelOrderCount: { value: 23, remark: "当前渠道活跃订单数量" },
          orderIntermediateCount: { value: 12, remark: "处于中间状态的订单数量（如处理中）" },
          currentOrderIntermediateCount: { value: 8, remark: "当前处于中间状态的订单数量" },
          invoiceDeliveryTimeoutCount: { value: 1, remark: "配送超时的订单数量" },
          invoiceCount: { value: 26, remark: "已发货订单数量" },
          invoiceCountShippedCount: { value: 25, remark: "已发货的订单数量" },
          invoiceCountnNotShippedCount: { value: 1, remark: "未发货的发票数量" },
          currentInvoiceCount: { value: 3, remark: "当前处理中的发票数量" },
          currentInvoiceCountShippedCount: { value: 22, remark: "当前已发货的发票数量" },
          currentInvoiceCountnNotShippedCount: { value: 1, remark: "当前未发货的发票数量" },
          EnterDate: { value: "2025-06-16 21:00:18.730", remark: "记录创建时间" },
          LastUpdate: { value: "2025-06-16 21:00:18.730", remark: "最后更新时间" },
          BOPS_CompanyID: { value: 10001, remark: "关联公司ID，用于企业级数据区分" }
        },
        // Overstock 渠道数据
        {
          Id: { value: 9, remark: "日志id" },
          ChannelName: { value: "emks-overstock", remark: "渠道名称，如电商平台标识" },
          ChannelAccount: { value: "<EMAIL>", remark: "渠道账号，用于登录或关联账户" },
          channelOrderCount: { value: 29, remark: "渠道总订单数量" },
          currentChannelOrderCount: { value: 19, remark: "当前渠道活跃订单数量" },
          orderIntermediateCount: { value: 10, remark: "处于中间状态的订单数量（如处理中）" },
          currentOrderIntermediateCount: { value: 6, remark: "当前处于中间状态的订单数量" },
          invoiceDeliveryTimeoutCount: { value: 1, remark: "配送超时的订单数量" },
          invoiceCount: { value: 22, remark: "已发货订单数量" },
          invoiceCountShippedCount: { value: 21, remark: "已发货的订单数量" },
          invoiceCountnNotShippedCount: { value: 1, remark: "未发货的发票数量" },
          currentInvoiceCount: { value: 2, remark: "当前处理中的发票数量" },
          currentInvoiceCountShippedCount: { value: 18, remark: "当前已发货的发票数量" },
          currentInvoiceCountnNotShippedCount: { value: 1, remark: "当前未发货的发票数量" },
          EnterDate: { value: "2025-06-16 03:00:18.730", remark: "记录创建时间" },
          LastUpdate: { value: "2025-06-16 03:00:18.730", remark: "最后更新时间" },
          BOPS_CompanyID: { value: 10001, remark: "关联公司ID，用于企业级数据区分" }
        },
        // 10th channel data
        {
          Id: { value: 10, remark: "日志id" },
          ChannelName: { value: "emks-channel10", remark: "渠道名称，如电商平台标识" },
          ChannelAccount: { value: "<EMAIL>", remark: "渠道账号，用于登录或关联账户" },
          channelOrderCount: { value: 30, remark: "渠道总订单数量" },
          currentChannelOrderCount: { value: 20, remark: "当前渠道活跃订单数量" },
          orderIntermediateCount: { value: 15, remark: "处于中间状态的订单数量（如处理中）" },
          currentOrderIntermediateCount: { value: 10, remark: "当前处于中间状态的订单数量" },
          invoiceDeliveryTimeoutCount: { value: 2, remark: "配送超时的订单数量" },
          invoiceCount: { value: 25, remark: "已发货订单数量" },
          invoiceCountShippedCount: { value: 23, remark: "已发货的订单数量" },
          invoiceCountnNotShippedCount: { value: 2, remark: "未发货的发票数量" },
          currentInvoiceCount: { value: 3, remark: "当前处理中的发票数量" },
          currentInvoiceCountShippedCount: { value: 18, remark: "当前已发货的发票数量" },
          currentInvoiceCountnNotShippedCount: { value: 1, remark: "当前未发货的发票数量" },
          EnterDate: { value: "2025-06-15 03:00:18.730", remark: "记录创建时间" },
          LastUpdate: { value: "2025-06-15 03:00:18.730", remark: "最后更新时间" },
          BOPS_CompanyID: { value: 10001, remark: "关联公司ID，用于企业级数据区分" }
        }
      ]
    };
  },
  mounted() {
    this.calculationTableHeight();
    this.loadData();
  },
  computed: {
    columns() {
      return [
        {
          label: '渠道名称',
          field: 'ChannelName',
          hide: false,
          width: null,
          align: 'center',
          formatter: (row) => row.ChannelName.value
        },
        {
          label: '渠道账号',
          field: 'ChannelAccount',
          hide: false,
          width: null,
          align: 'center',
          formatter: (row) => row.ChannelAccount.value
        },
        {
          label: '总订单数量',
          field: 'channelOrderCount',
          hide: false,
          width: null,
          align: 'center',
          formatter: (row) => row.channelOrderCount.value
        },
        {
          label: '活跃订单数量',
          field: 'currentChannelOrderCount',
          hide: false,
          width: null,
          align: 'center',
          formatter: (row) => row.currentChannelOrderCount.value
        },
        {
          label: '中间状态订单数量',
          field: 'currentOrderIntermediateCount',
          hide: false,
          width: null,
          align: 'center',
          formatter: (row) => row.currentOrderIntermediateCount.value
        },
        {
          label: '已发货订单数量',
          field: 'currentInvoiceCountShippedCount',
          hide: false,
          width: null,
          align: 'center',
          formatter: (row) => row.currentInvoiceCountShippedCount.value
        },
        {
          label: '未发货订单数量',
          field: 'currentInvoiceCountnNotShippedCount',
          hide: false,
          width: null,
          align: 'center',
          formatter: (row) => row.currentInvoiceCountnNotShippedCount.value
        },
        {
          label: '记录时间',
          field: 'EnterDate',
          hide: false,
          width: null,
          align: 'center',
          formatter: (row) => row.EnterDate.value
        }
      ];
    },
    showColumns() {
      return this.columns.filter((item) => !item.hide);
    }
  },
  methods: {
    getRowKeys(row) {
      return row.Id.value;
    },
    calculationTableHeight() {
      this.$nextTick(() => {
        try {
          const windowHeight = window.innerHeight;
          // 减去图表区域高度(约400px)、搜索区域高度(约100px)、页面边距等
          this.tableHeight = windowHeight - 550;
          if (this.tableHeight < 300) {
            this.tableHeight = 300; // 最小高度
          }
        } catch (error) {
          console.error('计算表格高度失败', error);
          this.tableHeight = 400; // 默认高度
        }
      });
    },
    handleDateRangeChange(val) {
      if (val) {
        this.params.condition.startDate = val[0];
        this.params.condition.endDate = val[1];
      } else {
        this.params.condition.startDate = '';
        this.params.condition.endDate = '';
      }
    },
    clearSearchItem(item) {
      if (item && item.prop) {
        this.params.condition[item.prop] = '';
      }
      this.handleFilter();
    },
    afterFilterLayoutTable() {
      this.calculationTableHeight();
    },
    handleFilter() {
      this.params.current = 1;
      this.loadData();
    },
    sortChangeHandle({ column, prop, order }) {
      if (order) {
        this.params.sorts = [
          {
            field: CommonUtil.camelToSnake ? CommonUtil.camelToSnake(prop) : prop,
            asc: order === 'ascending',
          },
        ];
      } else {
        delete this.params.sorts;
      }
      this.loadData();
    },
    // 表格样式方法
    getTableHeaderStyle() {
      return {
        background: '#F5F7FA',
        color: '#606266',
        height: '50px',
        padding: '0',
        fontWeight: 'bold'
      };
    },
    getTableCell() {
      return {
        padding: '5px 0'
      };
    },
    tableHeaderDragend(newWidth, prop, key) {
      if (CommonUtil.saveTableColumnWidth) {
        CommonUtil.saveTableColumnWidth(key, prop, newWidth);
      }
    },
    loadData() {
      this.result.loading = true;
      
      // 模拟API调用
      setTimeout(() => {
        // 过滤数据
        let filteredData = [...this.mockData];
        const condition = this.params.condition;
        
        if (condition.channelName) {
          filteredData = filteredData.filter(item => 
            item.ChannelName.value.toLowerCase().includes(condition.channelName.toLowerCase())
          );
        }
        
        if (condition.channelAccount) {
          filteredData = filteredData.filter(item => 
            item.ChannelAccount.value.toLowerCase().includes(condition.channelAccount.toLowerCase())
          );
        }
        
        if (condition.startDate && condition.endDate) {
          filteredData = filteredData.filter(item => {
            const date = new Date(item.EnterDate.value);
            const start = new Date(condition.startDate);
            const end = new Date(condition.endDate);
            return date >= start && date <= end;
          });
        }
        
        // 分页
        const start = (this.params.current - 1) * this.params.size;
        const end = start + this.params.size;
        const paginatedData = filteredData.slice(start, end);
        
        this.result.list = paginatedData;
        this.result.total = filteredData.length;
        this.result.loading = false;
      }, 500);
    }
  }
};
</script>

<style lang="scss" scoped>
.order-monitoring-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  padding: 20px;
  
  .chart-container {
    flex-shrink: 0;
    margin-bottom: 20px;
    
    .chart-box {
      background-color: #fff;
      border-radius: 4px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      padding: 15px;
      
      .chart-title {
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 15px;
        color: #303133;
      }
      
      .chart-content {
        height: 300px;
      }
    }
  }
  
  .table-container {
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }
}
</style>






